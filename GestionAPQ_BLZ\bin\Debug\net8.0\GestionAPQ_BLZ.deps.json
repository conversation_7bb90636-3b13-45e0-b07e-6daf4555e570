{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"GestionAPQ_BLZ/1.0.0": {"dependencies": {"Blazr.RenderState.Server": "1.0.0", "Common.ResponseModels": "1.0.0", "DevExpress.AspNetCore.Reporting": "24.2.8", "DevExpress.Blazor": "24.2.8", "DevExpress.Blazor.Reporting.JSBasedControls": "24.2.8", "GestionAPQ_BLZ.Client": "1.0.0", "Logging.Shared": "1.0.0", "MediatR": "12.5.0", "Microsoft.AspNetCore.Components.WebAssembly.Server": "8.0.15", "Microsoft.EntityFrameworkCore.SqlServer": "8.0.15", "TinyMapper": "3.0.3"}, "runtime": {"GestionAPQ_BLZ.dll": {}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "9.0.5", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "8.0.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}}, "Ben.Demystifier/0.4.1": {"dependencies": {"System.Reflection.Metadata": "5.0.0"}, "runtime": {"lib/netstandard2.1/Ben.Demystifier.dll": {"assemblyVersion": "0.4.0.0", "fileVersion": "0.4.0.2"}}}, "Blazr.RenderState/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "8.0.15"}, "runtime": {"lib/net8.0/Blazr.RenderState.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Blazr.RenderState.Server/1.0.0": {"dependencies": {"Blazr.RenderState": "1.0.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}, "runtime": {"lib/net8.0/Blazr.RenderState.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Common.ResponseModels/1.0.0": {"runtime": {"lib/net8.0/Common.ResponseModels.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DevExpress.AspNetCore.Common/24.2.8": {"dependencies": {"DevExpress.AspNetCore.Core": "24.2.8", "DevExpress.Data": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.AspNetCore.Common.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.AspNetCore.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Office.Core": "24.2.8", "Microsoft.Extensions.DependencyModel": "8.0.2"}, "runtime": {"lib/net8.0/DevExpress.AspNetCore.Core.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.AspNetCore.Reporting/24.2.8": {"dependencies": {"DevExpress.AspNetCore.Common": "24.2.8", "DevExpress.AspNetCore.Core": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.DataAccess": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Reporting.Core": "24.2.8", "DevExpress.Web.Reporting.Common": "24.2.8", "DevExpress.Web.Reporting.Common.Services": "24.2.8", "DevExpress.Web.Resources": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.AspNetCore.Reporting.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Blazor/24.2.8": {"dependencies": {"DevExpress.Blazor.Resources": "24.2.8", "DevExpress.Blazor.Themes": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExtreme.AspNet.Data": "4.0.0", "System.Drawing.Common": "4.7.2", "System.Reactive": "5.0.0"}, "runtime": {"lib/net8.0/DevExpress.Blazor.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Blazor.Reporting.JSBasedControls/24.2.8": {"dependencies": {"DevExpress.Blazor.Reporting.JSBasedControls.Common": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.DataAccess": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Reporting.Core": "24.2.8", "DevExpress.Web.Reporting.Common": "24.2.8", "DevExpress.Web.Reporting.Common.Services": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.Blazor.Reporting.v24.2.JSBasedControls.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Blazor.Reporting.JSBasedControls.Common/24.2.8": {"dependencies": {"DevExpress.Blazor.Resources": "24.2.8", "DevExpress.Data": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.Blazor.Reporting.v24.2.JSBasedControls.Common.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Blazor.Resources/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.Blazor.Resources.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Blazor.Themes/24.2.8": {}, "DevExpress.Charts/24.2.8": {"dependencies": {"DevExpress.Charts.Core": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.DataVisualization.Core": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.XtraCharts.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Charts.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.Charts.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.CodeParser/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "System.CodeDom": "4.4.0"}, "runtime": {"lib/net8.0/DevExpress.CodeParser.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Data/24.2.8": {"dependencies": {"System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.Data.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.DataAccess/24.2.8": {"dependencies": {"DevExpress.CodeParser": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Office.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.RichEdit.Core": "24.2.8", "DevExpress.Xpo": "24.2.8", "System.Configuration.ConfigurationManager": "8.0.1", "System.Data.SqlClient": "4.8.6"}, "runtime": {"lib/net8.0/DevExpress.DataAccess.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.DataVisualization.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.DataVisualization.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Drawing/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.Drawing.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Gauges.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.XtraGauges.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Office.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.Office.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Pdf.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net8.0/DevExpress.Pdf.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Pdf.Drawing/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.Pdf.v24.2.Drawing.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.PivotGrid.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "System.Data.OleDb": "8.0.1", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.PivotGrid.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Printing.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "DevExpress.Pdf.Drawing": "24.2.8", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1", "System.ServiceModel.Http": "6.2.0"}, "runtime": {"lib/net8.0/DevExpress.Printing.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Reporting.Core/24.2.8": {"dependencies": {"DevExpress.Charts": "24.2.8", "DevExpress.Charts.Core": "24.2.8", "DevExpress.CodeParser": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.DataAccess": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Gauges.Core": "24.2.8", "DevExpress.Office.Core": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "DevExpress.Pdf.Drawing": "24.2.8", "DevExpress.PivotGrid.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.RichEdit.Core": "24.2.8", "DevExpress.RichEdit.Export": "24.2.8", "DevExpress.Sparkline.Core": "24.2.8", "DevExpress.Xpo": "24.2.8", "System.CodeDom": "4.4.0", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.XtraReports.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.RichEdit.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Office.Core": "24.2.8", "DevExpress.Pdf.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.RichEdit.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.RichEdit.Export/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Office.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.RichEdit.Core": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.RichEdit.v24.2.Export.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Sparkline.Core/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.Drawing": "24.2.8", "System.Drawing.Common": "4.7.2"}, "runtime": {"lib/net8.0/DevExpress.Sparkline.v24.2.Core.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Web.Reporting.Common/24.2.8": {"dependencies": {"DevExpress.Charts": "24.2.8", "DevExpress.Charts.Core": "24.2.8", "DevExpress.Data": "24.2.8", "DevExpress.DataAccess": "24.2.8", "DevExpress.Drawing": "24.2.8", "DevExpress.Gauges.Core": "24.2.8", "DevExpress.PivotGrid.Core": "24.2.8", "DevExpress.Printing.Core": "24.2.8", "DevExpress.Reporting.Core": "24.2.8", "DevExpress.Sparkline.Core": "24.2.8", "DevExpress.Xpo": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.XtraReports.v24.2.Web.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Web.Reporting.Common.Services/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "DevExpress.DataAccess": "24.2.8", "DevExpress.Reporting.Core": "24.2.8", "DevExpress.Web.Reporting.Common": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.Web.Reporting.v24.2.Common.Services.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Web.Resources/24.2.8": {"dependencies": {"DevExpress.AspNetCore.Core": "24.2.8", "DevExpress.Data": "24.2.8"}, "runtime": {"lib/net8.0/DevExpress.Web.Resources.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExpress.Xpo/24.2.8": {"dependencies": {"DevExpress.Data": "24.2.8", "Microsoft.Extensions.DependencyInjection": "8.0.1", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1", "System.ServiceModel.Http": "6.2.0", "System.ServiceModel.NetTcp": "6.2.0"}, "runtime": {"lib/net8.0/DevExpress.Xpo.v24.2.dll": {"assemblyVersion": "24.2.8.0", "fileVersion": "24.2.8.0"}}}, "DevExtreme.AspNet.Data/4.0.0": {"runtime": {"lib/net6.0/DevExtreme.AspNet.Data.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "Logging.Shared/1.0.0": {"dependencies": {"MediatR": "12.5.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Serilog": "4.3.0", "Serilog.AspNetCore": "8.0.3", "Serilog.Enrichers.AspNetCore": "1.0.0", "Serilog.Enrichers.AspNetCore.HttpContext": "1.0.1", "Serilog.Enrichers.CallerInfo": "1.0.5", "Serilog.Enrichers.Thread": "4.0.0", "Serilog.Exceptions.EntityFrameworkCore": "8.4.0", "Serilog.Sinks.Async": "2.1.0", "Serilog.Sinks.MSSqlServer": "8.2.0"}, "runtime": {"lib/net8.0/Logging.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MediatR/12.5.0": {"dependencies": {"MediatR.Contracts": "2.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net6.0/MediatR.dll": {"assemblyVersion": "1*******", "fileVersion": "12.5.0.0"}}}, "MediatR.Contracts/2.0.1": {"runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "Microsoft.AspNetCore.Authorization/8.0.15": {"dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.15", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.AspNetCore.Components/8.0.15": {"dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.15", "Microsoft.AspNetCore.Components.Analyzers": "8.0.15"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.AspNetCore.Components.Analyzers/8.0.15": {}, "Microsoft.AspNetCore.Components.Forms/8.0.15": {"dependencies": {"Microsoft.AspNetCore.Components": "8.0.15"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.AspNetCore.Components.Web/8.0.15": {"dependencies": {"Microsoft.AspNetCore.Components": "8.0.15", "Microsoft.AspNetCore.Components.Forms": "8.0.15", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0", "Microsoft.JSInterop": "8.0.15", "System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.AspNetCore.Components.WebAssembly/8.0.15": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "8.0.15", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.JSInterop.WebAssembly": "8.0.15"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.AspNetCore.Components.WebAssembly.Server/8.0.15": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.WebAssembly.Server.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.AspNetCore.Metadata/8.0.15": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.0.3": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.0.2", "Microsoft.Net.Http.Headers": "2.0.2"}}, "Microsoft.AspNetCore.Routing.Abstractions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.WebUtilities/2.0.2": {"dependencies": {"Microsoft.Net.Http.Headers": "2.0.2", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/5.2.2": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.1", "System.Runtime.Caching": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}}, "resources": {"lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}, "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.22.24240.6"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "5.2.0.0"}}}, "Microsoft.EntityFrameworkCore/8.0.15": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.15", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.15", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1525.16502"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.15": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1525.16502"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.15": {}, "Microsoft.EntityFrameworkCore.Relational/8.0.15": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.15", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1525.16502"}}}, "Microsoft.EntityFrameworkCore.SqlServer/8.0.15": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.2", "Microsoft.EntityFrameworkCore.Relational": "8.0.15", "System.Formats.Asn1": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1525.16502"}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}}}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyModel/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "8.0.0.2", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "8.0.2", "System.Diagnostics.DiagnosticSource": "9.0.5"}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.5"}}, "Microsoft.Extensions.Logging/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "System.Diagnostics.DiagnosticSource": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.ObjectPool/6.0.16": {}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "9.0.5"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Logging/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.35.0", "System.IdentityModel.Tokens.Jwt": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Tokens/6.35.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.35.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.JSInterop/8.0.15": {"runtime": {"lib/net8.0/Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.JSInterop.WebAssembly/8.0.15": {"dependencies": {"Microsoft.JSInterop": "8.0.15"}, "runtime": {"lib/net8.0/Microsoft.JSInterop.WebAssembly.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1525.16505"}}}, "Microsoft.Net.Http.Headers/2.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0", "System.Buffers": "4.4.0"}}, "Microsoft.NETCore.Platforms/3.1.4": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "Serilog/4.3.0": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.AspNetCore/8.0.3": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.1", "Serilog": "4.3.0", "Serilog.Extensions.Hosting": "8.0.0", "Serilog.Formatting.Compact": "2.0.0", "Serilog.Settings.Configuration": "8.0.4", "Serilog.Sinks.Console": "5.0.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net8.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.AspNetCore/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.0.3", "Microsoft.AspNetCore.WebUtilities": "2.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Serilog": "4.3.0"}, "runtime": {"lib/netstandard2.0/Serilog.Enrichers.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.AspNetCore.HttpContext/1.0.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "8.0.2", "Serilog": "4.3.0"}, "runtime": {"lib/netstandard2.0/Serilog.Enrichers.AspNetCore.HttpContext.dll": {"assemblyVersion": "1.0.1.0", "fileVersion": "1.0.1.0"}}}, "Serilog.Enrichers.CallerInfo/1.0.5": {"dependencies": {"Ben.Demystifier": "0.4.1", "Serilog": "4.3.0"}, "runtime": {"lib/net6.0/Serilog.Enrichers.CallerInfo.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Thread/4.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "Serilog.Exceptions/8.4.0": {"dependencies": {"Serilog": "4.3.0", "System.Reflection.TypeExtensions": "4.7.0"}, "runtime": {"lib/net6.0/Serilog.Exceptions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Exceptions.EntityFrameworkCore/8.4.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.15", "Serilog.Exceptions": "8.4.0"}, "runtime": {"lib/net6.0/Serilog.Exceptions.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Serilog": "4.3.0", "Serilog.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.1", "Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/2.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net7.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/8.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyModel": "8.0.2", "Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Async/2.1.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Async.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/5.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net7.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.MSSqlServer/8.2.0": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.2", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Serilog": "4.3.0", "System.Configuration.ConfigurationManager": "8.0.1"}, "runtime": {"lib/net8.0/Serilog.Sinks.MSSqlServer.dll": {"assemblyVersion": "8.2.0.0", "fileVersion": "8.2.0.0"}}}, "System.Buffers/4.4.0": {}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.CodeDom/4.4.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.6.25519.3"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Configuration.ConfigurationManager/8.0.1": {"dependencies": {"System.Diagnostics.EventLog": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Data.OleDb/8.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1", "System.Diagnostics.PerformanceCounter": "8.0.1"}, "runtime": {"lib/net8.0/System.Data.OleDb.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Data.OleDb.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Data.SqlClient/4.8.6": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/9.0.5": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Diagnostics.EventLog/8.0.1": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Diagnostics.PerformanceCounter/8.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "runtime": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Drawing.Common/4.7.2": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.Win32.SystemEvents": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Drawing.Common.dll": {"assemblyVersion": "4.0.0.2", "fileVersion": "4.6.29719.1"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "4.0.2.2", "fileVersion": "4.700.21.6905"}, "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.2.2", "fileVersion": "4.700.21.6905"}}}, "System.Formats.Asn1/8.0.2": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Pipelines/8.0.0": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reactive/5.0.0": {"runtime": {"lib/net5.0/System.Reactive.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/5.0.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.7.0": {}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "runtime": {"lib/net8.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.Pkcs/8.0.1": {"runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Cryptography.Xml/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "8.0.1"}}, "System.Security.Principal.Windows/4.7.0": {}, "System.ServiceModel.Http/6.2.0": {"dependencies": {"System.ServiceModel.Primitives": "6.2.0"}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.23.55602"}}, "resources": {"lib/net6.0/cs/System.ServiceModel.Http.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.Http.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.Http.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.Http.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.Http.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.Http.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.Http.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.Http.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.Http.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.Http.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.Http.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.Http.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.Http.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.NetFramingBase/6.2.0": {"dependencies": {"System.ServiceModel.Primitives": "6.2.0"}, "runtime": {"lib/net6.0/System.ServiceModel.NetFramingBase.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.23.55602"}}, "resources": {"lib/net6.0/cs/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.NetTcp/6.2.0": {"dependencies": {"System.ServiceModel.NetFramingBase": "6.2.0", "System.ServiceModel.Primitives": "6.2.0"}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.23.55602"}}, "resources": {"lib/net6.0/cs/System.ServiceModel.NetTcp.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.NetTcp.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.NetTcp.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.NetTcp.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.NetTcp.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.NetTcp.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.NetTcp.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.NetTcp.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.NetTcp.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.NetTcp.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.NetTcp.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.NetTcp.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.NetTcp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.Primitives/6.2.0": {"dependencies": {"Microsoft.Extensions.ObjectPool": "6.0.16", "System.Security.Cryptography.Xml": "6.0.1"}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.23.55602"}}, "resources": {"lib/net6.0/cs/System.ServiceModel.Primitives.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.Primitives.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.Primitives.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.Primitives.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.Primitives.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.Primitives.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.Primitives.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.Primitives.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.Primitives.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.Primitives.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.Primitives.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web/4.7.2": {}, "System.Text.Json/4.7.2": {}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "TinyMapper/3.0.3": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.TypeExtensions": "4.7.0"}, "runtime": {"lib/netstandard1.3/TinyMapper.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "GestionAPQ_BLZ.Client/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Components.WebAssembly": "8.0.15"}, "runtime": {"GestionAPQ_BLZ.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"GestionAPQ_BLZ/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "Ben.Demystifier/0.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-axFeEMfmEORy3ipAzOXG/lE+KcNptRbei3F0C4kQCdeiQtW+qJW90K5iIovITGrdLt8AjhNCwk5qLSX9/rFpoA==", "path": "ben.demystifier/0.4.1", "hashPath": "ben.demystifier.0.4.1.nupkg.sha512"}, "Blazr.RenderState/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JMT7jZsTWHVs1gwPOjEvnlBkLZiB6vWjTV8S6hcO4fkXxtj1/cHMDyjwGTfalXmthkCs3RrI+5l0dV5ct92/1g==", "path": "blazr.renderstate/1.0.0", "hashPath": "blazr.renderstate.1.0.0.nupkg.sha512"}, "Blazr.RenderState.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZBwIFm2lDui7JC2684+9sIiZ6ycEvrX4WSDit1q2qHlauDmXND5+9LbeuX8Hi4NhO9GwvgycnGVXL9l3hAhUZg==", "path": "blazr.renderstate.server/1.0.0", "hashPath": "blazr.renderstate.server.1.0.0.nupkg.sha512"}, "Common.ResponseModels/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WNMbfi6SkIO1T7wMqLSgdNWJlBvjOYSx+ndiRnuhciGtURSos2kj7mjYqaFlubbi9jcMQfZbFBRmZyZu+nwjcA==", "path": "common.responsemodels/1.0.0", "hashPath": "common.responsemodels.1.0.0.nupkg.sha512"}, "DevExpress.AspNetCore.Common/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-dM6uiCqiZ0XWW0F1i2Je1oRZvSsmRAAQnhaVUXIrKr6fgvAmtq9YFhCL1tWA+BAFxM44cLSaTvkekWS7VD7Xhg==", "path": "devexpress.aspnetcore.common/24.2.8", "hashPath": "devexpress.aspnetcore.common.24.2.8.nupkg.sha512"}, "DevExpress.AspNetCore.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-Mx9RwHIbYguKRHIgkFpvxjiavvoG7HJIShrd08k5GGpexkOuzbCJbbWVvHMOE7zXRyRQC7vRYdpv1jKReha1zQ==", "path": "devexpress.aspnetcore.core/24.2.8", "hashPath": "devexpress.aspnetcore.core.24.2.8.nupkg.sha512"}, "DevExpress.AspNetCore.Reporting/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-Grd0qCXGoJ/jpIuysyNUjh6xhlSnjlyfS974QpzMv+nJt9vAkOtf32jUdmPGb2S4s8H+Kr//7t3mxj5q9aHSuA==", "path": "devexpress.aspnetcore.reporting/24.2.8", "hashPath": "devexpress.aspnetcore.reporting.24.2.8.nupkg.sha512"}, "DevExpress.Blazor/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-Y2BQIp+SF2mGlW1cPI+Hva/job0/ksV+tAUoGa3Kho3QysJu21ufuhfg253gpDc6g0FNPAVCgIrPTKVtcenr8g==", "path": "devexpress.blazor/24.2.8", "hashPath": "devexpress.blazor.24.2.8.nupkg.sha512"}, "DevExpress.Blazor.Reporting.JSBasedControls/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-l/SX92SytW3HDmIhBqGTaaERn5thoVNYaZTnDFCnxzgLFuXbBw+2L3RIGZwdnQbuxcAGL46LdyMuML0D0sQe7w==", "path": "devexpress.blazor.reporting.jsbasedcontrols/24.2.8", "hashPath": "devexpress.blazor.reporting.jsbasedcontrols.24.2.8.nupkg.sha512"}, "DevExpress.Blazor.Reporting.JSBasedControls.Common/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zKRHXK106+xNWIJq7fB72UutMKOM/l9CYUs2QNUNlaYnARXATYDtyn3p/362Y3+txU/cbfkBaDdeKe1HPjGY5g==", "path": "devexpress.blazor.reporting.jsbasedcontrols.common/24.2.8", "hashPath": "devexpress.blazor.reporting.jsbasedcontrols.common.24.2.8.nupkg.sha512"}, "DevExpress.Blazor.Resources/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-LuyEsAnSqM+t2nL8+2RTLwj1XBVLJndh0DHAX96rHCUYnqswm+0A8+9PpWkWrhGoTl76nCwPhK5lXZfELFNnqQ==", "path": "devexpress.blazor.resources/24.2.8", "hashPath": "devexpress.blazor.resources.24.2.8.nupkg.sha512"}, "DevExpress.Blazor.Themes/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-jujD4hb3UdpIDISS+H1FKV5gwb7oR5FYVrCrjwWfJkx7JJk/Qm8jxsXrHxZTNebW8oCpiWFpPicELdEOnNXsPg==", "path": "devexpress.blazor.themes/24.2.8", "hashPath": "devexpress.blazor.themes.24.2.8.nupkg.sha512"}, "DevExpress.Charts/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-FKxDEiaNzZgtmaLHH0XBGWgx9p3L5RbWcU9ebOqAJqCMk/loZvIuU9832QBvE+5CTaw+RNSeS6bS230dUk+cTw==", "path": "devexpress.charts/24.2.8", "hashPath": "devexpress.charts.24.2.8.nupkg.sha512"}, "DevExpress.Charts.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-afW8PCkGZMApYDk5TdXEApLd6cXVM561fW8Mmx1/F5Q64m+w93WQCYV71WfYBnFnwXBWC0vzvjxBkhfMJ5qrBA==", "path": "devexpress.charts.core/24.2.8", "hashPath": "devexpress.charts.core.24.2.8.nupkg.sha512"}, "DevExpress.CodeParser/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-KN8X1RifFCPaaaazFcA+/ZLuG3c7VmEqNbnkgwcqTh74TBQzCa7heoBII/lIJJI7Ym52Ut3yndiLTSCck5oz+g==", "path": "devexpress.codeparser/24.2.8", "hashPath": "devexpress.codeparser.24.2.8.nupkg.sha512"}, "DevExpress.Data/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-6G7pQcsYF7kTgfWJfYEHtbjtZJSHC7V2+IdcTx+OHWq8Xu51EDE8b2B1j5s+blqBeKXyQcYCQAAexsMHHacAVw==", "path": "devexpress.data/24.2.8", "hashPath": "devexpress.data.24.2.8.nupkg.sha512"}, "DevExpress.DataAccess/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-dyA97gfesHQGDJBSL8A8PZFUz1XQkZFmOZoCV4xpMDW0pF9+8ybj05mkeRS3cKIPgTarLALnXHsUD9fRlTZGvg==", "path": "devexpress.dataaccess/24.2.8", "hashPath": "devexpress.dataaccess.24.2.8.nupkg.sha512"}, "DevExpress.DataVisualization.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-2hoKvgmdw2Rld9rL0dWPYG2D6OjZVRUoVrDvc1H1JhvRq/F/rei2MYTYFy7GJyIo+5JB0gtmex4L5SN8CRsPCA==", "path": "devexpress.datavisualization.core/24.2.8", "hashPath": "devexpress.datavisualization.core.24.2.8.nupkg.sha512"}, "DevExpress.Drawing/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-ALdAEAFWb3Ooyz6dUxZARZaCBlB/HN8sa3ivuW7SZUrbysnJ2nNJ0H0UaTvNIkaZ5K62+8oiomTiHEnqiWOKwA==", "path": "devexpress.drawing/24.2.8", "hashPath": "devexpress.drawing.24.2.8.nupkg.sha512"}, "DevExpress.Gauges.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-CUhJ9zkVUw5ePu5GFHCxJSKrNTPhef0HbGktiD5HXTHG8OVNURFSlPw8yPsFEji3VHuP0KNVT6wQDEbCCruwjg==", "path": "devexpress.gauges.core/24.2.8", "hashPath": "devexpress.gauges.core.24.2.8.nupkg.sha512"}, "DevExpress.Office.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-yTl8zP51s2AErC2EiBFzfs6l4wwQ4vEiIZd+qC7FvJMVVTjzTIPgQnHGjOGLT6RUIc6FEREi0U9Ss1yi5PFs2w==", "path": "devexpress.office.core/24.2.8", "hashPath": "devexpress.office.core.24.2.8.nupkg.sha512"}, "DevExpress.Pdf.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-0bslRmvczovySN8kiWMotFl4LoYqcCAfMABEiNGIe/P76XmUnnHZJnjgB+ejZ/MdDLYsK6E/GMV85h35B+R7FA==", "path": "devexpress.pdf.core/24.2.8", "hashPath": "devexpress.pdf.core.24.2.8.nupkg.sha512"}, "DevExpress.Pdf.Drawing/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-FRsvrggRVGWL2JYMC4hgjQzh+D1uBu3GAGzwd8JNwwxWAi4RzKDcYAU+c4EQlXqUwHH7aTfnHac5BVnqsGHkmQ==", "path": "devexpress.pdf.drawing/24.2.8", "hashPath": "devexpress.pdf.drawing.24.2.8.nupkg.sha512"}, "DevExpress.PivotGrid.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-m0ugcNdjISklEqGjPEgNQ8kvnOg1iUNTkTIEesPYpQXywEM3T/Hl1uGzeGaCEOevnVeOzmUZa0ZEGhNhLn6WZA==", "path": "devexpress.pivotgrid.core/24.2.8", "hashPath": "devexpress.pivotgrid.core.24.2.8.nupkg.sha512"}, "DevExpress.Printing.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-j0B/vRWs0O7RugLtfd7cOrosL20CIB5cA1+iyc04mOqoozrnK0XX4u7e38Th1hKl42nPkV+Ijs9HLjAb/MvzdQ==", "path": "devexpress.printing.core/24.2.8", "hashPath": "devexpress.printing.core.24.2.8.nupkg.sha512"}, "DevExpress.Reporting.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-vj6+v34P/CFH4w6dQyNszfAg8PMiW9PzWM/9IeFdoXq1AH76GwXm1uunUjatUUJFrjkMcU3xlyd1ENXglVu5GQ==", "path": "devexpress.reporting.core/24.2.8", "hashPath": "devexpress.reporting.core.24.2.8.nupkg.sha512"}, "DevExpress.RichEdit.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-SMiLKJCxh0pB7ldQKym7H+S2zmTfQupRmyfV0u63+np77BKOO99GYe1INO6/jvhaAu4IHhAd0lrViDUEBzVkPg==", "path": "devexpress.richedit.core/24.2.8", "hashPath": "devexpress.richedit.core.24.2.8.nupkg.sha512"}, "DevExpress.RichEdit.Export/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-Dpo0j65prtoBaZeEaLqrpvRcWwWkhKKLgomUDjWth9D/x+O3HTxQGpkznAx8UTnTf+iDiB74cqiQ18BufX/2cA==", "path": "devexpress.richedit.export/24.2.8", "hashPath": "devexpress.richedit.export.24.2.8.nupkg.sha512"}, "DevExpress.Sparkline.Core/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-4RU+tiVcZYl6p/Ltrq2CH3rObIfZj/wpFKMWW/64Gu7+a2Wn/1z8yB4HM6oYI/N8YeuUb80ebpk2LD0zIf2CsQ==", "path": "devexpress.sparkline.core/24.2.8", "hashPath": "devexpress.sparkline.core.24.2.8.nupkg.sha512"}, "DevExpress.Web.Reporting.Common/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-tfSFGcEV+wMA/Tm9Y/eDQZ6z0LRXwhdvIhyBxnvR7TNQGzu5oZg/Ni1mJj1M5EzSjGKJw08OISRoGo4Py98xAA==", "path": "devexpress.web.reporting.common/24.2.8", "hashPath": "devexpress.web.reporting.common.24.2.8.nupkg.sha512"}, "DevExpress.Web.Reporting.Common.Services/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-/nIKDGbtryaOWWnvPtHZ49sSqAtuQkBJ0htFfCeyEIEklwmmEOJO+W7OS+SzwFzHKd491U51Ua6EDwtWjx1nZA==", "path": "devexpress.web.reporting.common.services/24.2.8", "hashPath": "devexpress.web.reporting.common.services.24.2.8.nupkg.sha512"}, "DevExpress.Web.Resources/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-294xUUQPKne3enOMor75CJjNA9LuADE4H63hkJZne7QyUOLqJ3vXaxeD18zWzQL/TecaI6dhWZ4E+55lna/YPw==", "path": "devexpress.web.resources/24.2.8", "hashPath": "devexpress.web.resources.24.2.8.nupkg.sha512"}, "DevExpress.Xpo/24.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-1i16FLh/MRG/CAXFDiR+mLNMux919tbEHN6OW0A6ApF38iU9LEQe3JkVewfGeZda3KNixuH2rmsHX77VIm8cHQ==", "path": "devexpress.xpo/24.2.8", "hashPath": "devexpress.xpo.24.2.8.nupkg.sha512"}, "DevExtreme.AspNet.Data/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-BfADnQ9HD3L1/du53CrDbqfb+3zTBc+yGYG1sblYKL33cf2BEjljUEAopI6K4i35fcR4pI6dUbsQL4qOMrRqZg==", "path": "devextreme.aspnet.data/4.0.0", "hashPath": "devextreme.aspnet.data.4.0.0.nupkg.sha512"}, "Logging.Shared/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OcR4KiuIDz/K3ZZCu76cWbYdktBP8usLFRv/8mP6+tW8Xln9Glnm8S1SfkK1b6MRli0J4XbLXsNimrsay4f4dQ==", "path": "logging.shared/1.0.0", "hashPath": "logging.shared.1.0.0.nupkg.sha512"}, "MediatR/12.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-vqm2H8/nqL5NAJHPhsG1JOPwfkmbVrPyh4svdoRzu+uZh6Ex7PRoHBGsLYC0/RWCEJFqD1ohHNpteQvql9OktA==", "path": "mediatr/12.5.0", "hashPath": "mediatr.12.5.0.nupkg.sha512"}, "MediatR.Contracts/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "path": "mediatr.contracts/2.0.1", "hashPath": "mediatr.contracts.2.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-AIpppyCOYv0hJjmYqytMSVohAV8THV6sTk7VoM6rHo1Jq0D1fOfkRi9lnmEz20nfKt50wX2OlciEQop3JlMC6A==", "path": "microsoft.aspnetcore.authorization/8.0.15", "hashPath": "microsoft.aspnetcore.authorization.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Components/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-eQIgPU1oTkqR8hWvDjyZFGBBtgPY4bYl4a/WN7g0txmoww+fbE1nZ4D+5MeSbZu1kfHWX/Ruog/pMF9zUX6D7g==", "path": "microsoft.aspnetcore.components/8.0.15", "hashPath": "microsoft.aspnetcore.components.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-Uqc/wecFqwfpC9bUK9DH3W3nrriRM6ZBL8OozV6GHNNgrqvzdnqsPKo3NcmjhokvMo2/XHTrreRC2yA2aTLS/A==", "path": "microsoft.aspnetcore.components.analyzers/8.0.15", "hashPath": "microsoft.aspnetcore.components.analyzers.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-579O5c3u0FXnOAjk6JNzcy+/+qazMbedAeOy1vT1B6CBYovG8iGJhyUukqCbHcNQq3/lqxBTyB9rFzlH5zOttA==", "path": "microsoft.aspnetcore.components.forms/8.0.15", "hashPath": "microsoft.aspnetcore.components.forms.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-ttRxlR+yM0sNi+Ecw0ze26l2IcBIF2L+YNQGs4v159v+Ge51yn8bwehnlzs9MY5y3Xamvek4l71uZfk1oouYSA==", "path": "microsoft.aspnetcore.components.web/8.0.15", "hashPath": "microsoft.aspnetcore.components.web.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-KFugRNQGBQINzcMcoaTzZr8t30kGuamOtHCmn38Bygx2/FdDJ3UuHvEm0ALMttUNoD5ewQyJPqBsi3olH/LXoQ==", "path": "microsoft.aspnetcore.components.webassembly/8.0.15", "hashPath": "microsoft.aspnetcore.components.webassembly.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly.Server/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-MUux2oI92MELxT+a5GWVMoKD3wxfUWsMQzjY1oUz+J8jA2DEOOK6jykOXR94rI555rXNaBGf7wrO4w6difNAkQ==", "path": "microsoft.aspnetcore.components.webassembly.server/8.0.15", "hashPath": "microsoft.aspnetcore.components.webassembly.server.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-eKlqQasjNZD0B3thB+nX7kkpG/RiZlXObmjX+JurWycnssGp1q9iZjKn2SqKA45Pouw+529aQUYYP2/QCE5xIQ==", "path": "microsoft.aspnetcore.metadata/8.0.15", "hashPath": "microsoft.aspnetcore.metadata.8.0.15.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-iXPYz6zZE6vLLJYjQA7F8vtyPqYgOR1bOhChkfuhbIzrU4VELB2I3ozOdMGviXlmApbpRXZKd4z7viqlKKXiIg==", "path": "microsoft.aspnetcore.mvc.abstractions/2.0.3", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-sqI4xsQYm/11KsY8P892yrpL3ALAp6e6u12mrnbdWhQt/IiWhK4X9OIQVVMM+ofrPkAKsjP96ctEkJcDKysNVw==", "path": "microsoft.aspnetcore.routing.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dvn80+p1AIQKOfJ+VrOhVMUktWRvJs7Zb+UapZGBNSyrCzTsYiXbb9C7Mzw+nGj5UevnLNFcWWc7BUlLMD2qpw==", "path": "microsoft.aspnetcore.webutilities/2.0.2", "hashPath": "microsoft.aspnetcore.webutilities.2.0.2.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-mtoeRMh7F/OA536c/Cnh8L4H0uLSKB5kSmoi54oN7Fp0hNJDy22IqyMhaMH4PkDCqI7xL//Fvg9ldtuPHG0h5g==", "path": "microsoft.data.sqlclient/5.2.2", "hashPath": "microsoft.data.sqlclient.5.2.2.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w==", "path": "microsoft.data.sqlclient.sni.runtime/5.2.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-v6gDYdi2j+BbUHzl5sb835mefpFlSCsQOOsn/xjky45kFA9Xrc2hjVa0yQGVLc2J7+fQDqoHWRD5c+RHU3klSQ==", "path": "microsoft.entityframeworkcore/8.0.15", "hashPath": "microsoft.entityframeworkcore.8.0.15.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-wzBUYqfNTyclVaVo6we4Vo0GsfNSmHHJPn3SF4gEsR+e5QZ4r5Ck5Y9oRcEoeqxS+eFPvJzWN6xpTbwCCoWPnw==", "path": "microsoft.entityframeworkcore.abstractions/8.0.15", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.15.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-viyTqFw09xwhynmvYggUu48R0Y6WEU2dDKBKm7WwozDH5t1K7+RS9P+iXGe5uUNJstTH/STd2AMFKpT9GaX4OQ==", "path": "microsoft.entityframeworkcore.analyzers/8.0.15", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.15.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-rOge9UqPepLr4fDSbAjH+gMl4TwK7byKgUy66vgsjlwl11n/+MjbYZAYqfrD8mpGDwJ6Nzv2LIIK2fBHDqA+CQ==", "path": "microsoft.entityframeworkcore.relational/8.0.15", "hashPath": "microsoft.entityframeworkcore.relational.8.0.15.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-Kouryy99RU+YvRpfxJ4+ajaILxindm/Fq67zFBd8Lzikl7k/b9c7Ow5ilIB7anrhh6tUXc2MIHfwDV1AcrQS/g==", "path": "microsoft.entityframeworkcore.sqlserver/8.0.15", "hashPath": "microsoft.entityframeworkcore.sqlserver.8.0.15.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "path": "microsoft.extensions.caching.memory/8.0.1", "hashPath": "microsoft.extensions.caching.memory.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-7IQhGK+wjyGrNsPBjJcZwWAr+Wf6D4+TwOptUt77bWtgNkiV8tDEbhFS+dDamtQFZ2X7kWG9m71iZQRj2x3zgQ==", "path": "microsoft.extensions.configuration.binder/8.0.2", "hashPath": "microsoft.extensions.configuration.binder.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-EJzSNO9oaAXnTdtdNO6npPRsIIeZCBSNmdQ091VDO7fBiOtJAAeEq6dtrVXIi3ZyjC5XRSAtVvF8SzcneRHqKQ==", "path": "microsoft.extensions.configuration.fileextensions/8.0.1", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-L89DLNuimOghjV3tLx0ArFDwVEJD6+uGB3BMCMX01kaLzXkaXHb2021xOMl2QOxUxbdePKUZsUY7n2UUkycjRg==", "path": "microsoft.extensions.configuration.json/8.0.1", "hashPath": "microsoft.extensions.configuration.json.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "path": "microsoft.extensions.dependencyinjection/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-mUBDZZRgZrSyFOsJ2qJJ9fXfqd/kXJwf3AiDoqLD9m6TjY5OO/vLNOb9fb4juC0487eq4hcGN/M2Rh/CKS7QYw==", "path": "microsoft.extensions.dependencymodel/8.0.2", "hashPath": "microsoft.extensions.dependencymodel.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "path": "microsoft.extensions.logging/8.0.1", "hashPath": "microsoft.extensions.logging.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "path": "microsoft.extensions.logging.abstractions/9.0.5", "hashPath": "microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-OVX5tlKg6LY+XKqlUn7i9KY+6Liut0iewWff2DNr7129i/NJ8rpUzbmxavPydZgcLREEWHklXZiPKCS895tNIQ==", "path": "microsoft.extensions.objectpool/6.0.16", "hashPath": "microsoft.extensions.objectpool.6.0.16.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "path": "microsoft.identitymodel.abstractions/6.35.0", "hashPath": "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-jePrSfGAmqT81JDCNSY+fxVWoGuJKt9e6eJ+vT7+quVS55nWl//jGjUQn4eFtVKt4rt5dXaleZdHRB9J9AJZ7Q==", "path": "microsoft.identitymodel.logging/6.35.0", "hashPath": "microsoft.identitymodel.logging.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-BPQhlDzdFvv1PzaUxNSk+VEPwezlDEVADIKmyxubw7IiELK18uJ06RQ9QKKkds30XI+gDu9n8j24XQ8w7fjWcg==", "path": "microsoft.identitymodel.protocols/6.35.0", "hashPath": "microsoft.identitymodel.protocols.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-LMtVqnECCCdSmyFoCOxIE5tXQqkOLrvGrL7OxHg41DIm1bpWtaCdGyVcTAfOQpJXvzND9zUKIN/lhngPkYR8vg==", "path": "microsoft.identitymodel.protocols.openidconnect/6.35.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-RN7lvp7s3Boucg1NaNAbqDbxtlLj5Qeb+4uSS1TeK5FSBVM40P4DKaTKChT43sHyKfh7V0zkrMph6DdHvyA4bg==", "path": "microsoft.identitymodel.tokens/6.35.0", "hashPath": "microsoft.identitymodel.tokens.6.35.0.nupkg.sha512"}, "Microsoft.JSInterop/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-JQjYabj1o3M5rUp/GM8uVlLpgc9tgxZea+89IkvGl8AcY7Tfwn0Q5qltVa9rfm3mzCsr2ecVCKzWMSMYiXS0Kg==", "path": "microsoft.jsinterop/8.0.15", "hashPath": "microsoft.jsinterop.8.0.15.nupkg.sha512"}, "Microsoft.JSInterop.WebAssembly/8.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-L7Rnicz9pvb3d3vSLHOHTM8SKv248aFWH9RhH/RB6+siUygIKRah+r+6hJX98is01ygfuoKocdU6uYOQ954rAg==", "path": "microsoft.jsinterop.webassembly/8.0.15", "hashPath": "microsoft.jsinterop.webassembly.8.0.15.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-hNhJU+Sd7Ws/yrBnakUWKWMyGiDUJE5lTkJfWe5xPL8YGTiL6Es07H9CcTyaYYwVlgW06uDVN0YhhH+t4EjdCw==", "path": "microsoft.net.http.headers/2.0.2", "hashPath": "microsoft.net.http.headers.2.0.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-9/y05/CuxE+j184Nr4KihhB9KcUkvGojmD4JV4Vt/mHhVZR+eOCD5WCM+CXye9K0OFMsaPXbN+IcaIpjgBGZmg==", "path": "microsoft.netcore.platforms/3.1.4", "hashPath": "microsoft.netcore.platforms.3.1.4.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "path": "microsoft.win32.systemevents/4.7.0", "hashPath": "microsoft.win32.systemevents.4.7.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Serilog/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+cDryFR0GRhsGOnZSKwaDzRRl4MupvJ42FhCE4zhQRVanX0Jpg6WuCBk59OVhVDPmab1bB+nRykAnykYELA9qQ==", "path": "serilog/4.3.0", "hashPath": "serilog.4.3.0.nupkg.sha512"}, "Serilog.AspNetCore/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Y5at41mc0OV982DEJslBKHd6uzcWO6POwR3QceJ6gtpMPxCzm4+FElGPF0RdaTD7MGsP6XXE05LMbSi0NO+sXg==", "path": "serilog.aspnetcore/8.0.3", "hashPath": "serilog.aspnetcore.8.0.3.nupkg.sha512"}, "Serilog.Enrichers.AspNetCore/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-s1MTjjHxDPrSxUCKE5WvVaKlYEsT52z9sK94u1V5RqJpNncWzaa8ypg2zNyF/qsI5tn5qP4q0MUQsf6eyvkcYg==", "path": "serilog.enrichers.aspnetcore/1.0.0", "hashPath": "serilog.enrichers.aspnetcore.1.0.0.nupkg.sha512"}, "Serilog.Enrichers.AspNetCore.HttpContext/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fcD2pJ3p7iqdsp/L+i9vBkSQN6hvLR6NGGcfOm81tcYfLEOqlZ8FIN07sfQtBZ7sLX3UBrXAlciYmswVG4lIgQ==", "path": "serilog.enrichers.aspnetcore.httpcontext/1.0.1", "hashPath": "serilog.enrichers.aspnetcore.httpcontext.1.0.1.nupkg.sha512"}, "Serilog.Enrichers.CallerInfo/1.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Y6WI4dv8c7DUcy1tMUOPi/jcmut2r0eqyc0bM3lKzi8Z7SWoM7L+pUOJbgzkwL1u4QnErYst3VuH9Y0/KTJPdg==", "path": "serilog.enrichers.callerinfo/1.0.5", "hashPath": "serilog.enrichers.callerinfo.1.0.5.nupkg.sha512"}, "Serilog.Enrichers.Thread/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C7BK25a1rhUyr+Tp+1BYcVlBJq7M2VCHlIgnwoIUVJcicM9jYcvQK18+OeHiXw7uLPSjqWxJIp1EfaZ/RGmEwA==", "path": "serilog.enrichers.thread/4.0.0", "hashPath": "serilog.enrichers.thread.4.0.0.nupkg.sha512"}, "Serilog.Exceptions/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-nc/+hUw3lsdo0zCj0KMIybAu7perMx79vu72w0za9Nsi6mWyNkGXxYxakAjWB7nEmYL6zdmhEQRB4oJ2ALUeug==", "path": "serilog.exceptions/8.4.0", "hashPath": "serilog.exceptions.8.4.0.nupkg.sha512"}, "Serilog.Exceptions.EntityFrameworkCore/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-3mOul1jW79IL6CVGpaX3zdaMXpKv20/X/gg9D10kHfabjQ35s7aNYeT3Rm3uXA0MovfURB/41aosKcyfboBNCQ==", "path": "serilog.exceptions.entityframeworkcore/8.4.0", "hashPath": "serilog.exceptions.entityframeworkcore.8.4.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-db0OcbWeSCvYQkHWu6n0v40N4kKaTAXNjlM3BKvcbwvNzYphQFcBR+36eQ/7hMMwOkJvAyLC2a9/jNdUL5NjtQ==", "path": "serilog.extensions.hosting/8.0.0", "hashPath": "serilog.extensions.hosting.8.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YEAMWu1UnWgf1c1KP85l1SgXGfiVo0Rz6x08pCiPOIBt2Qe18tcZLvdBUuV5o1QHvrs8FAry9wTIhgBRtjIlEg==", "path": "serilog.extensions.logging/8.0.0", "hashPath": "serilog.extensions.logging.8.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ob6z3ikzFM3D1xalhFuBIK1IOWf+XrQq+H4KeH4VqBcPpNcmUgZlRQ2h3Q7wvthpdZBBoY86qZOI2LCXNaLlNA==", "path": "serilog.formatting.compact/2.0.0", "hashPath": "serilog.formatting.compact.2.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-pkxvq0umBKK8IKFJc1aV5S/HGRG/NIxJ6FV42KaTPLfDmBOAbBUB1m5gqqlGxzEa1MgDDWtQlWJdHTSxVWNx+Q==", "path": "serilog.settings.configuration/8.0.4", "hashPath": "serilog.settings.configuration.8.0.4.nupkg.sha512"}, "Serilog.Sinks.Async/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-SnmRknWsSMgyo9wDXeZZCqSp48kkQYy44taSM6vcpxfiRICzSf09oLKEmVr0RCwQnfd8mJQ2WNN6nvhqf0RowQ==", "path": "serilog.sinks.async/2.1.0", "hashPath": "serilog.sinks.async.2.1.0.nupkg.sha512"}, "Serilog.Sinks.Console/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IZ6bn79k+3SRXOBpwSOClUHikSkp2toGPCZ0teUkscv4dpDg9E2R2xVsNkLmwddE4OpNVO3N0xiYsAH556vN8Q==", "path": "serilog.sinks.console/5.0.0", "hashPath": "serilog.sinks.console.5.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Serilog.Sinks.MSSqlServer/8.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iFDMXySagOYQGFF7l27I1YxKZFk9T3hNnpvA6mY8GolOctYNZJja+j1k2Mjc+t2XxqkQBdUxIEPt3ujtwwfWTg==", "path": "serilog.sinks.mssqlserver/8.2.0", "hashPath": "serilog.sinks.mssqlserver.8.2.0.nupkg.sha512"}, "System.Buffers/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-AwarXzzoDwX6BgrhjoJsk6tUezZEozOT5Y9QKF94Gl4JK91I4PIIBkBco9068Y9/Dra8Dkbie99kXB8+1BaYKw==", "path": "system.buffers/4.4.0", "hashPath": "system.buffers.4.4.0.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LE/oChpRvkSi3U25u0KnJcI44JeDZ1QJCyN4qFDx2uusEypdqR24w7lKYw21eYe5esuCBuc862wRmpF63Yy1KQ==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "path": "system.configuration.configurationmanager/8.0.1", "hashPath": "system.configuration.configurationmanager.8.0.1.nupkg.sha512"}, "System.Data.OleDb/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-RO+/y2ggU5956uQDRXdjA1e2l5yJ4rTWNX76eZ+3sgtYGqGapCe2kQCyiUci+/y6Fyb21Irp4RQEdfrIiuYrxQ==", "path": "system.data.oledb/8.0.1", "hashPath": "system.data.oledb.8.0.1.nupkg.sha512"}, "System.Data.SqlClient/4.8.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "path": "system.data.sqlclient/4.8.6", "hashPath": "system.data.sqlclient.4.8.6.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-WoI5or8kY2VxFdDmsaRZ5yaYvvb+4MCyy66eXo79Cy1uMa7qXeGIlYmZx7R9Zy5S4xZjmqvkk2V8L6/vDwAAEA==", "path": "system.diagnostics.diagnosticsource/9.0.5", "hashPath": "system.diagnostics.diagnosticsource.9.0.5.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-n1ZP7NM2Gkn/MgD8+eOT5MulMj6wfeQMNS2Pizvq5GHCZfjlFMXV2irQlQmJhwA2VABC57M0auudO89Iu2uRLg==", "path": "system.diagnostics.eventlog/8.0.1", "hashPath": "system.diagnostics.eventlog.8.0.1.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9RfEDiEjlUADeThs8IPdDVTXSnPRSqjfgTQJALpmGFPKC0k2mbdufOXnb/9JZ4I0TkmxOfy3VTJxrHOJSs8cXg==", "path": "system.diagnostics.performancecounter/8.0.1", "hashPath": "system.diagnostics.performancecounter.8.0.1.nupkg.sha512"}, "System.Drawing.Common/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-I2y4KBK3VCvU/WqE2xv7NjQ67maXHttkFSHYKgU2evrG9Yqh0oFjfORXt5hZTk+BVjdyFo2h0/YQZsca33BGmg==", "path": "system.drawing.common/4.7.2", "hashPath": "system.drawing.common.4.7.2.nupkg.sha512"}, "System.Formats.Asn1/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-yUsFqNGa7tbwm5QOOnOR3VSoh8a0Yki39mTbhOnErdbg8hVSFtrK0EXerj286PXcegiF1LkE7lL++qqMZW5jIQ==", "path": "system.formats.asn1/8.0.2", "hashPath": "system.formats.asn1.8.0.2.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "path": "system.identitymodel.tokens.jwt/6.35.0", "hashPath": "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reactive/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "path": "system.reactive/5.0.0", "hashPath": "system.reactive.5.0.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "path": "system.reflection.metadata/5.0.0", "hashPath": "system.reflection.metadata.5.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VybpaOQQhqE6siHppMktjfGBw1GCwvCqiufqmP8F1nj7fTUNtW35LOEt3UZTEsECfo+ELAl/9o9nJx3U91i7vA==", "path": "system.reflection.typeextensions/4.7.0", "hashPath": "system.reflection.typeextensions.4.7.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4TmlmvGp4kzZomm7J2HJn6IIx0UUrQyhBDyb5O1XiunZlQImXW+B8b7W/sTPcXhSf9rp5NR5aDtQllwbB5elOQ==", "path": "system.runtime.caching/8.0.0", "hashPath": "system.runtime.caching.8.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "path": "system.security.cryptography.pkcs/8.0.1", "hashPath": "system.security.cryptography.pkcs.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "path": "system.security.cryptography.xml/6.0.1", "hashPath": "system.security.cryptography.xml.6.0.1.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.ServiceModel.Http/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lMk8MEw1OCvyyKY4HMg4ro1eYtWY7azIoDc2FBEGP8uOTJouWn3DemOQvM/GUpgrFbkpjuHPbEG5hgUbNtpiYA==", "path": "system.servicemodel.http/6.2.0", "hashPath": "system.servicemodel.http.6.2.0.nupkg.sha512"}, "System.ServiceModel.NetFramingBase/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-204c9SNDKyQrDKv6F9MLlWKnM7UthRErFByJCHj8y9DtcgMAQnEB5xJvh+9ECmJgG13LJLOAMB5f3CjMatzz/A==", "path": "system.servicemodel.netframingbase/6.2.0", "hashPath": "system.servicemodel.netframingbase.6.2.0.nupkg.sha512"}, "System.ServiceModel.NetTcp/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXTDhh8DgCfNyY5k9sNlqvhBVYqVM+0GZBsJfFMH5P5q7qGmTxql3bG9tae1Z+uMXJpG2jLbo1CfgusZ75lADA==", "path": "system.servicemodel.nettcp/6.2.0", "hashPath": "system.servicemodel.nettcp.6.2.0.nupkg.sha512"}, "System.ServiceModel.Primitives/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ro+c4JKNuX6dDpTWh9ZICYr4pIe7uJToauPPgZt2qqFPjVB78ZDUz3rPCZX89dA+IoRZ+9T1ngLBKsgkTmx7UA==", "path": "system.servicemodel.primitives/6.2.0", "hashPath": "system.servicemodel.primitives.6.2.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "path": "system.text.json/4.7.2", "hashPath": "system.text.json.4.7.2.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "TinyMapper/3.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-a070h+NbFjMMclmWjEjGDnihUs4wE3ykx7R4BG7Oo38Jf66spzc3h3CbvbJYFlkbYl65dt5vbJFFH72vFdRr1A==", "path": "tinymapper/3.0.3", "hashPath": "tinymapper.3.0.3.nupkg.sha512"}, "GestionAPQ_BLZ.Client/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}