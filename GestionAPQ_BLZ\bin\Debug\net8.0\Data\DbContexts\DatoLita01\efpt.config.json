﻿{
    "CodeGenerationMode": 4,
    "ContextClassName": "DatoLita01Context",
    "ContextNamespace": null,
    "FilterSchemas": false,
    "IncludeConnectionString": true,
    "ModelNamespace": null,
    "OutputContextPath": "Data\\DbContexts\\DatoLita01",
    "OutputPath": "Data\\Entities\\DatoLita01",
    "PreserveCasingWithRegex": true,
    "ProjectRootNamespace": "GestionAQP_BLZ.Server",
    "Schemas": null,
    "SelectedHandlebarsLanguage": 2,
    "SelectedToBeGenerated": 0,
    "T4TemplatePath": null,
    "Tables": [
        {
            "Name": "[dbo].[A_ARTICU]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[A_ENTDIA]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[A_LOTESS]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[A_SALIDA]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[A_VISCOS]",
            "ObjectType": 0
        }
    ],
    "UiHint": null,
    "UncountableWords": null,
    "UseAsyncStoredProcedureCalls": true,
    "UseBoolPropertiesWithoutDefaultSql": false,
    "UseDatabaseNames": false,
    "UseDatabaseNamesForRoutines": true,
    "UseDateOnlyTimeOnly": false,
    "UseDbContextSplitting": false,
    "UseDecimalDataAnnotationForSprocResult": true,
    "UseFluentApiOnly": true,
    "UseHandleBars": false,
    "UseHierarchyId": false,
    "UseInflector": false,
    "UseInternalAccessModifiersForSprocsAndFunctions": false,
    "UseLegacyPluralizer": false,
    "UseManyToManyEntity": false,
    "UseNoDefaultConstructor": false,
    "UseNoNavigations": false,
    "UseNoObjectFilter": false,
    "UseNodaTime": false,
    "UseNullableReferences": false,
    "UsePrefixNavigationNaming": false,
    "UseSchemaFolders": false,
    "UseSchemaNamespaces": false,
    "UseSpatial": false,
    "UseT4": false,
    "UseT4Split": false
}