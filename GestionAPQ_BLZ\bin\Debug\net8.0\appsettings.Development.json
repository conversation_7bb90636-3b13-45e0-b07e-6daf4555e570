{
    "AppMetadata": {
        "ProjectName": "GestionAPQ_BLZ",
        "ProjectNameDisplay": "Gestión APQ"
    },
    "DetailedErrors": true, // turns on CircuitOptions.DetailedErrors
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning"
        }
    },
    "AllowedHosts": "*",
    "ConnectionStrings": {
        "DefaultConnectionDataWarehouse":
            "Server=QPLANT1;Database=Lital**DataWarehouse;User ID=**;Password=**;MultipleActiveResultSets=True; Encrypt=False; TrustServerCertificate=True;",
        "DefaultConnectionDatoLita01":
            "Server=SERVIN21\\SQL2014;Database=dato01LITA; User ID=**;Password=**;MultipleActiveResultSets=True; Encrypt=False; TrustServerCertificate=True;",
        "DefaultConnectionCalidadLital**":
            "Server=QPLANT1;Database=CalidadLital**;User ID=**;Password=**;MultipleActiveResultSets=True; Encrypt=False; TrustServerCertificate=True;",
        "DefaultConnectionApqLital**":
            "Server=QPLANT1;Database=ApqLital**;User ID=**;Password=**;MultipleActiveResultSets=True; Encrypt=False; TrustServerCertificate=True;"
    }
}