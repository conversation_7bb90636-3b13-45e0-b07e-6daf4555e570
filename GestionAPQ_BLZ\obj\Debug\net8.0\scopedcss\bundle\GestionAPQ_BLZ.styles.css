@import 'GestionAPQ_BLZ.Client.5k9b6nvr3m.bundle.scp.css';

/* _content/GestionAPQ_BLZ/Components/Layout/MainLayout.razor.rz.scp.css */
.navbar-header[b-oomczt62fx] {
    background-color: #a91c32;
    border: none;
    border-radius: 0;
    box-shadow: 8px 2px 6px rgba(0, 0, 0, 0.35);
    flex-grow: 0;
    flex-wrap: nowrap;
    height: 3.5rem;
    min-height: 3.5rem;
}

.contenedor-icono-hamburger[b-oomczt62fx] {
    max-width: 65px;
    min-width: 65px;
    width: 65px;
}

.contenedor-icono-sidebar[b-oomczt62fx] {
    max-width: 36px;
    min-width: 36px;
    width: 36px;
}

.border-end[b-oomczt62fx] { border-right-color: rgba(34, 34, 34, 0.15) !important; }

.navbar-toggler-icon[b-oomczt62fx] {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255,255,255, 1)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 8h24M4 16h24M4 24h24'/%3E%3C/svg%3E");
    height: 2rem;
    width: 2rem;
}

.icono-sidebar[b-oomczt62fx] { font-size: 1.25rem; }

.nav-item[b-oomczt62fx] {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

.nav-item:first-of-type[b-oomczt62fx] { padding-top: 1rem; }

.nav-item:last-of-type[b-oomczt62fx] { padding-bottom: 1rem; }

.nav-item[b-oomczt62fx]  .nav-link { padding: 0.5rem 5px !important; }

.nav-item[b-oomczt62fx]  a {
    align-items: center;
    border-radius: 4px;
    color: #d7d7d7;
    display: flex;
    height: 3rem;
    line-height: 3rem;
}

.nav-item[b-oomczt62fx]  a.active {
    background-color: rgba(255, 255, 255, 0.25);
    color: white;
}

.nav-item[b-oomczt62fx]  a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}
/* _content/GestionAPQ_BLZ/Components/Pages/ControlPesosEnvases.razor.rz.scp.css */
.popup-pesos-envases[b-yt6csrd1u3] {
    width: 850px !important;
    max-width: 95vw !important;
}
/* _content/GestionAPQ_BLZ/Components/Pages/Home.razor.rz.scp.css */
[b-f64d3hntjo] .ch-320 {
    height: 285px !important;
    max-height: 285px !important;
}

.height-responsive-botonera[b-f64d3hntjo] { height: auto; }

@media (min-width: 768px) {
    .height-responsive-botonera[b-f64d3hntjo] {
        height: 285px; /* valor ch-320 */
    }
}
