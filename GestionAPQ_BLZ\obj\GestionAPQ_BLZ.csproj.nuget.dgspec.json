{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ.Client\\GestionAPQ_BLZ.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ.Client\\GestionAPQ_BLZ.Client.csproj", "projectName": "GestionAPQ_BLZ.Client", "projectPath": "C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ.Client\\GestionAPQ_BLZ.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ.Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "\\\\dc1\\basesdatos\\Nugets": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.devexpress.com/bBEsu1fcNg3j5nDB7vS5tMYM5MEeAGDbvhEuRJUrMlechyNUJq/api/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Components.WebAssembly": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.18, )", "autoReferenced": true}, "Microsoft.NET.Sdk.WebAssembly.Pack": {"suppressParent": "All", "target": "Package", "version": "[9.0.7, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Runtime.Mono.browser-wasm", "version": "[8.0.18, 8.0.18]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"browser-wasm": {"#import": []}}}, "C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ.csproj", "projectName": "GestionAPQ_BLZ", "projectPath": "C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "\\\\dc1\\basesdatos\\Nugets": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.devexpress.com/bBEsu1fcNg3j5nDB7vS5tMYM5MEeAGDbvhEuRJUrMlechyNUJq/api/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ.Client\\GestionAPQ_BLZ.Client.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ.Client\\GestionAPQ_BLZ.Client.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Blazr.RenderState.Server": {"target": "Package", "version": "[1.0.0, )"}, "Common.ResponseModels": {"target": "Package", "version": "[1.0.0, )"}, "DevExpress.AspNetCore.Reporting": {"target": "Package", "version": "[24.2.8, )"}, "DevExpress.Blazor": {"target": "Package", "version": "[24.2.8, )"}, "DevExpress.Blazor.Reporting.JSBasedControls": {"target": "Package", "version": "[24.2.8, )"}, "Logging.Shared": {"target": "Package", "version": "[1.0.0, )"}, "MediatR": {"target": "Package", "version": "[12.5.0, )"}, "Microsoft.AspNetCore.Components.WebAssembly.Server": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.15, )"}, "TinyMapper": {"target": "Package", "version": "[3.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}