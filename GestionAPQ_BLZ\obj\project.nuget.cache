{"version": 2, "dgSpecHash": "+wLPUXIOZ9w=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Documents\\WorkSpace\\Repos\\BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ\\GestionAPQ_BLZ.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.38.0\\azure.core.1.38.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.identity\\1.11.4\\azure.identity.1.11.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\ben.demystifier\\0.4.1\\ben.demystifier.0.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\blazr.renderstate\\1.0.0\\blazr.renderstate.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\blazr.renderstate.server\\1.0.0\\blazr.renderstate.server.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\common.responsemodels\\1.0.0\\common.responsemodels.1.0.0.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.aspnetcore.common\\24.2.8\\devexpress.aspnetcore.common.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.aspnetcore.core\\24.2.8\\devexpress.aspnetcore.core.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.aspnetcore.reporting\\24.2.8\\devexpress.aspnetcore.reporting.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.blazor\\24.2.8\\devexpress.blazor.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.blazor.reporting.jsbasedcontrols\\24.2.8\\devexpress.blazor.reporting.jsbasedcontrols.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.blazor.reporting.jsbasedcontrols.common\\24.2.8\\devexpress.blazor.reporting.jsbasedcontrols.common.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.blazor.resources\\24.2.8\\devexpress.blazor.resources.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.blazor.themes\\24.2.8\\devexpress.blazor.themes.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.charts\\24.2.8\\devexpress.charts.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.charts.core\\24.2.8\\devexpress.charts.core.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.codeparser\\24.2.8\\devexpress.codeparser.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.data\\24.2.8\\devexpress.data.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.dataaccess\\24.2.8\\devexpress.dataaccess.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.datavisualization.core\\24.2.8\\devexpress.datavisualization.core.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.drawing\\24.2.8\\devexpress.drawing.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.gauges.core\\24.2.8\\devexpress.gauges.core.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.office.core\\24.2.8\\devexpress.office.core.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.pdf.core\\24.2.8\\devexpress.pdf.core.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.pdf.drawing\\24.2.8\\devexpress.pdf.drawing.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.pivotgrid.core\\24.2.8\\devexpress.pivotgrid.core.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.printing.core\\24.2.8\\devexpress.printing.core.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.reporting.core\\24.2.8\\devexpress.reporting.core.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.richedit.core\\24.2.8\\devexpress.richedit.core.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.richedit.export\\24.2.8\\devexpress.richedit.export.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.sparkline.core\\24.2.8\\devexpress.sparkline.core.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.web.reporting.common\\24.2.8\\devexpress.web.reporting.common.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.web.reporting.common.services\\24.2.8\\devexpress.web.reporting.common.services.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.web.resources\\24.2.8\\devexpress.web.resources.24.2.8.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.xpo\\24.2.8\\devexpress.xpo.24.2.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devextreme.aspnet.data\\4.0.0\\devextreme.aspnet.data.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\logging.shared\\1.0.0\\logging.shared.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr\\12.5.0\\mediatr.12.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr.contracts\\2.0.1\\mediatr.contracts.2.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\8.0.15\\microsoft.aspnetcore.authorization.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components\\8.0.15\\microsoft.aspnetcore.components.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.analyzers\\8.0.15\\microsoft.aspnetcore.components.analyzers.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.forms\\8.0.15\\microsoft.aspnetcore.components.forms.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.web\\8.0.15\\microsoft.aspnetcore.components.web.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webassembly\\8.0.15\\microsoft.aspnetcore.components.webassembly.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webassembly.server\\8.0.15\\microsoft.aspnetcore.components.webassembly.server.8.0.15.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\8.0.15\\microsoft.aspnetcore.metadata.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.abstractions\\2.0.3\\microsoft.aspnetcore.mvc.abstractions.2.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.routing.abstractions\\2.0.2\\microsoft.aspnetcore.routing.abstractions.2.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.webutilities\\2.0.2\\microsoft.aspnetcore.webutilities.2.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\1.1.1\\microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\microsoft.csharp\\4.5.0\\microsoft.csharp.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient\\5.2.2\\microsoft.data.sqlclient.5.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient.sni.runtime\\5.2.0\\microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\8.0.15\\microsoft.entityframeworkcore.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\8.0.15\\microsoft.entityframeworkcore.abstractions.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.analyzers\\8.0.15\\microsoft.entityframeworkcore.analyzers.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\8.0.15\\microsoft.entityframeworkcore.relational.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlserver\\8.0.15\\microsoft.entityframeworkcore.sqlserver.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\8.0.0\\microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\8.0.1\\microsoft.extensions.caching.memory.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\8.0.0\\microsoft.extensions.configuration.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\8.0.0\\microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\8.0.2\\microsoft.extensions.configuration.binder.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\8.0.1\\microsoft.extensions.configuration.fileextensions.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\8.0.1\\microsoft.extensions.configuration.json.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.1\\microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.5\\microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\8.0.2\\microsoft.extensions.dependencymodel.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\8.0.0\\microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\8.0.0\\microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\8.0.0\\microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\8.0.0\\microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\8.0.0\\microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\8.0.1\\microsoft.extensions.logging.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.5\\microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\6.0.16\\microsoft.extensions.objectpool.6.0.16.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\8.0.2\\microsoft.extensions.options.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\8.0.0\\microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.61.3\\microsoft.identity.client.4.61.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client.extensions.msal\\4.61.3\\microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\6.35.0\\microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\6.35.0\\microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\6.35.0\\microsoft.identitymodel.logging.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\6.35.0\\microsoft.identitymodel.protocols.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\6.35.0\\microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\6.35.0\\microsoft.identitymodel.tokens.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop\\8.0.15\\microsoft.jsinterop.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop.webassembly\\8.0.15\\microsoft.jsinterop.webassembly.8.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.http.headers\\2.0.2\\microsoft.net.http.headers.2.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\3.1.4\\microsoft.netcore.platforms.3.1.4.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.sqlserver.server\\1.0.0\\microsoft.sqlserver.server.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\4.7.0\\microsoft.win32.systemevents.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.data.sqlclient.sni\\4.7.0\\runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\runtime.win-x64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\runtime.win-x86.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog\\4.3.0\\serilog.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.aspnetcore\\8.0.3\\serilog.aspnetcore.8.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.enrichers.aspnetcore\\1.0.0\\serilog.enrichers.aspnetcore.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.enrichers.aspnetcore.httpcontext\\1.0.1\\serilog.enrichers.aspnetcore.httpcontext.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.enrichers.callerinfo\\1.0.5\\serilog.enrichers.callerinfo.1.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.enrichers.thread\\4.0.0\\serilog.enrichers.thread.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.exceptions\\8.4.0\\serilog.exceptions.8.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.exceptions.entityframeworkcore\\8.4.0\\serilog.exceptions.entityframeworkcore.8.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.hosting\\8.0.0\\serilog.extensions.hosting.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.logging\\8.0.0\\serilog.extensions.logging.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.formatting.compact\\2.0.0\\serilog.formatting.compact.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.settings.configuration\\8.0.4\\serilog.settings.configuration.8.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.async\\2.1.0\\serilog.sinks.async.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.console\\5.0.0\\serilog.sinks.console.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.debug\\2.0.0\\serilog.sinks.debug.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.file\\5.0.0\\serilog.sinks.file.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.mssqlserver\\8.2.0\\serilog.sinks.mssqlserver.8.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.4.0\\system.buffers.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.clientmodel\\1.0.0\\system.clientmodel.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\4.4.0\\system.codedom.4.4.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.collections.nongeneric\\4.3.0\\system.collections.nongeneric.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.collections.specialized\\4.3.0\\system.collections.specialized.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.componentmodel.primitives\\4.3.0\\system.componentmodel.primitives.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.componentmodel.typeconverter\\4.3.0\\system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\8.0.1\\system.configuration.configurationmanager.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.oledb\\8.0.1\\system.data.oledb.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlclient\\4.8.6\\system.data.sqlclient.4.8.6.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\9.0.5\\system.diagnostics.diagnosticsource.9.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\8.0.1\\system.diagnostics.eventlog.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\8.0.1\\system.diagnostics.performancecounter.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\4.7.2\\system.drawing.common.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\8.0.2\\system.formats.asn1.8.0.2.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\6.35.0\\system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\8.0.0\\system.io.pipelines.8.0.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reactive\\5.0.0\\system.reactive.5.0.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\5.0.0\\system.reflection.metadata.5.0.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.7.0\\system.reflection.typeextensions.4.7.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.caching\\8.0.0\\system.runtime.caching.8.0.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.security.cryptography.cng\\4.5.0\\system.security.cryptography.cng.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\8.0.1\\system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\8.0.0\\system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\6.0.1\\system.security.cryptography.xml.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.7.0\\system.security.principal.windows.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.http\\6.2.0\\system.servicemodel.http.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.netframingbase\\6.2.0\\system.servicemodel.netframingbase.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.nettcp\\6.2.0\\system.servicemodel.nettcp.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.primitives\\6.2.0\\system.servicemodel.primitives.6.2.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\4.7.2\\system.text.encodings.web.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\4.7.2\\system.text.json.4.7.2.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\tinymapper\\3.0.3\\tinymapper.3.0.3.nupkg.sha512"], "logs": []}