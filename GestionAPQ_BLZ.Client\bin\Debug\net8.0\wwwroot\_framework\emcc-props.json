{"items": {"EmccProperties": [{"identity": "RuntimeEmccVersion", "value": "3.1.34"}, {"identity": "RuntimeEmccVersionRaw", "value": "emcc (Emscripten gcc/clang-like replacement + linker emulating GNU ld) 3.1.34 (57b21b8fdcbe3ebb523178b79465254668eab408)"}, {"identity": "RuntimeEmccVersionHash", "value": "57b21b8fdcbe3ebb523178b79465254668eab408"}], "WasmOptConfigurationFlags": [{"identity": "WasmOptConfigurationFlags", "value": " "}], "EmccDefaultExportedFunctions": ["_free", "_htons", "_malloc", "_memalign", "_memset", "_ntohs", "stackAlloc", "stackRestore", "stackSave", "_fmod", "_atan2", "_fma", "_pow", "_fmodf", "_atan2f", "_fmaf", "_powf", "_asin", "_asinh", "_acos", "_acosh", "_atan", "_atanh", "_cbrt", "_cos", "_cosh", "_exp", "_log", "_log2", "_log10", "_sin", "_sinh", "_tan", "_tanh", "_asinf", "_asinhf", "_acosf", "_acoshf", "_atanf", "_atanhf", "_cbrtf", "_cosf", "_coshf", "_expf", "_logf", "_log2f", "_log10f", "_sinf", "_sinhf", "_tanf", "_tanhf"], "EmccDefaultExportedRuntimeMethods": ["FS", "out", "err", "ccall", "cwrap", "setValue", "getValue", "UTF8ToString", "UTF8ArrayToString", "stringToUTF8Array", "FS_createPath", "FS_createDataFile", "removeRunDependency", "addRunDependency", "addFunction", "safeSetTimeout", "runtime<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "runtimeKeepalivePop", "maybeExit", "abort"]}}